# frozen_string_literal: true

require "spec_helper"

RSpec.describe Sentry::Rails::LogSubscribers::ActionMailerSubscriber do
  before do
    make_basic_app do |config|
      config.enable_logs = true
      config.rails.structured_logging.enabled = true
      config.rails.structured_logging.attach_to = [:action_mailer]
    end
  end

  describe "integration with ActiveSupport::Notifications" do
    it "logs deliver events when emails are sent" do
      sentry_transport.events.clear
      sentry_transport.envelopes.clear

      ActiveSupport::Notifications.instrument("deliver.action_mailer",
        mailer: "UserMailer",
        perform_deliveries: true,
        delivery_method: :test,
        date: Time.current,
        message_id: "<EMAIL>"
      ) do
        sleep(0.01)
      end

      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty

      log_event = sentry_logs.find { |log| log[:body] == "Email delivered via UserMailer" }
      expect(log_event).not_to be_nil
      expect(log_event[:level]).to eq("info")
      expect(log_event[:attributes][:mailer][:value]).to eq("UserMailer")
      expect(log_event[:attributes][:duration_ms][:value]).to be > 0
      expect(log_event[:attributes][:perform_deliveries][:value]).to be true
      expect(log_event[:attributes][:delivery_method][:value]).to eq(:test)
      expect(log_event[:attributes][:date]).to be_present
    end

    it "logs process events when mailer actions are processed" do
      sentry_transport.events.clear
      sentry_transport.envelopes.clear

      ActiveSupport::Notifications.instrument("process.action_mailer",
        mailer: "UserMailer",
        action: "welcome_email",
        params: { user_id: 123, name: "John Doe" }
      ) do
        sleep(0.01)
      end

      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty

      log_event = sentry_logs.find { |log| log[:body] == "UserMailer#welcome_email" }
      expect(log_event).not_to be_nil
      expect(log_event[:level]).to eq("info")
      expect(log_event[:attributes][:mailer][:value]).to eq("UserMailer")
      expect(log_event[:attributes][:action][:value]).to eq("welcome_email")
      expect(log_event[:attributes][:duration_ms][:value]).to be > 0
    end

    it "includes delivery method when available" do
      sentry_transport.events.clear
      sentry_transport.envelopes.clear

      ActiveSupport::Notifications.instrument("deliver.action_mailer",
        mailer: "NotificationMailer",
        perform_deliveries: true,
        delivery_method: :smtp
      )

      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty

      log_event = sentry_logs.find { |log| log[:body] == "Email delivered via NotificationMailer" }
      expect(log_event).not_to be_nil
      expect(log_event[:attributes][:delivery_method][:value]).to eq(:smtp)
    end

    context "when send_default_pii is enabled" do
      before do
        Sentry.configuration.send_default_pii = true
      end

      after do
        Sentry.configuration.send_default_pii = false
      end

      it "includes message_id for deliver events" do
        sentry_transport.events.clear
        sentry_transport.envelopes.clear

        ActiveSupport::Notifications.instrument("deliver.action_mailer",
          mailer: "UserMailer",
          perform_deliveries: true,
          message_id: "<EMAIL>"
        )

        Sentry.get_current_client.log_event_buffer.flush

        expect(sentry_logs).not_to be_empty

        log_event = sentry_logs.find { |log| log[:body] == "Email delivered via UserMailer" }
        expect(log_event).not_to be_nil
        expect(log_event[:attributes][:message_id][:value]).to eq("<EMAIL>")
      end

      it "includes filtered parameters for process events" do
        sentry_transport.events.clear
        sentry_transport.envelopes.clear

        ActiveSupport::Notifications.instrument("process.action_mailer",
          mailer: "UserMailer",
          action: "welcome_email",
          params: {
            user_id: 123,
            safe_param: "value",
            password: "secret",
            email_address: "<EMAIL>",
            subject: "Welcome!",
            api_key: "secret-key"
          }
        )

        Sentry.get_current_client.log_event_buffer.flush

        expect(sentry_logs).not_to be_empty

        log_event = sentry_logs.find { |log| log[:body] == "UserMailer#welcome_email" }
        expect(log_event).not_to be_nil
        expect(log_event[:attributes][:params]).to be_present

        params = log_event[:attributes][:params][:value]
        expect(params).to include(user_id: 123, safe_param: "value")
        expect(params).not_to have_key(:password)
        expect(params).not_to have_key(:email_address)
        expect(params).not_to have_key(:subject)
        expect(params).not_to have_key(:api_key)
      end
    end

    context "when send_default_pii is disabled" do
      it "does not include message_id for deliver events" do
        sentry_transport.events.clear
        sentry_transport.envelopes.clear

        ActiveSupport::Notifications.instrument("deliver.action_mailer",
          mailer: "UserMailer",
          perform_deliveries: true,
          message_id: "<EMAIL>"
        )

        Sentry.get_current_client.log_event_buffer.flush

        expect(sentry_logs).not_to be_empty

        log_event = sentry_logs.find { |log| log[:body] == "Email delivered via UserMailer" }
        expect(log_event).not_to be_nil
        expect(log_event[:attributes]).not_to have_key(:message_id)
      end

      it "does not include parameters for process events" do
        sentry_transport.events.clear
        sentry_transport.envelopes.clear

        ActiveSupport::Notifications.instrument("process.action_mailer",
          mailer: "UserMailer",
          action: "welcome_email",
          params: { user_id: 123, name: "John Doe" }
        )

        Sentry.get_current_client.log_event_buffer.flush

        expect(sentry_logs).not_to be_empty

        log_event = sentry_logs.find { |log| log[:body] == "UserMailer#welcome_email" }
        expect(log_event).not_to be_nil
        expect(log_event[:attributes]).not_to have_key(:params)
      end
    end

    it "excludes events starting with !" do
      subscriber = described_class.new
      event = double("event", name: "!connection.action_mailer", payload: {})
      expect(subscriber.send(:excluded_event?, event)).to be true
    end
  end

  describe "when logging is disabled" do
    before do
      make_basic_app do |config|
        config.enable_logs = false
        config.rails.structured_logging.enabled = true
        config.rails.structured_logging.attach_to = [:action_mailer]
      end
    end

    it "does not log events when logging is disabled" do
      initial_log_count = sentry_logs.count

      ActiveSupport::Notifications.instrument("deliver.action_mailer",
        mailer: "UserMailer",
        perform_deliveries: true
      )

      if Sentry.get_current_client&.log_event_buffer
        Sentry.get_current_client.log_event_buffer.flush
      end

      expect(sentry_logs.count).to eq(initial_log_count)
    end
  end

  describe "private method behavior" do
    let(:subscriber) { described_class.new }

    describe "#filter_sensitive_params" do
      it "filters email-related sensitive parameters" do
        params = {
          "user_id" => 123,
          "email_address" => "<EMAIL>",
          "to" => "<EMAIL>",
          "from" => "<EMAIL>",
          "subject" => "Test Subject",
          "body" => "Email body content"
        }
        filtered = subscriber.send(:filter_sensitive_params, params)

        expect(filtered).to include("user_id" => 123)
        expect(filtered).not_to have_key("email_address")
        expect(filtered).not_to have_key("to")
        expect(filtered).not_to have_key("from")
        expect(filtered).not_to have_key("subject")
        expect(filtered).not_to have_key("body")
      end

      it "filters authentication and security parameters" do
        params = {
          "name" => "John",
          "password" => "secret",
          "token" => "auth-token",
          "api_key" => "key123",
          "secret" => "secret-value"
        }
        filtered = subscriber.send(:filter_sensitive_params, params)

        expect(filtered).to include("name" => "John")
        expect(filtered).not_to have_key("password")
        expect(filtered).not_to have_key("token")
        expect(filtered).not_to have_key("api_key")
        expect(filtered).not_to have_key("secret")
      end

      it "filters personal data parameters" do
        params = {
          "id" => 1,
          "personal_data" => "sensitive info",
          "user_data" => "private info",
          "content" => "email content",
          "message" => "email message"
        }
        filtered = subscriber.send(:filter_sensitive_params, params)

        expect(filtered).to include("id" => 1)
        expect(filtered).not_to have_key("personal_data")
        expect(filtered).not_to have_key("user_data")
        expect(filtered).not_to have_key("content")
        expect(filtered).not_to have_key("message")
      end

      it "handles non-hash input gracefully" do
        expect(subscriber.send(:filter_sensitive_params, nil)).to eq({})
        expect(subscriber.send(:filter_sensitive_params, "string")).to eq({})
        expect(subscriber.send(:filter_sensitive_params, 123)).to eq({})
      end

      it "performs case-insensitive filtering" do
        params = {
          "PASSWORD" => "secret",
          "Email_Address" => "<EMAIL>",
          "API_KEY" => "key123",
          "SUBJECT" => "Test"
        }
        filtered = subscriber.send(:filter_sensitive_params, params)

        expect(filtered).to be_empty
      end
    end
  end
end
